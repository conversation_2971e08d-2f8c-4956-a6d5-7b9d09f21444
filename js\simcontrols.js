// JavaScript Document

var variableArray=[];
var connectionOptions=[];
var jockeySliderLbl;
var moveFlag=true;
var offsetX;
var offsetY;
var x;
var y;
var sliderValue;
var resCnst=0;
var resCnst1;
var resCnst2;
var resCnst3;
var resCnst4;
var resCnst5;
var resCnst6;
var resCnst7;
var resCnst8;
var W1;
var W2;
var Wireclk;
var Res="Single";
var batteryflag=0;
var resflag=0;
var wireflag=0;
var startFlag=false;
//slider fun:
var degree=0;
var rotate=0;
var position1;
var position2;
var position3;
var position4;
var position5;
var sliderValue=1;
var initialPosOftooltip=document.getElementById("tooltip").offsetLeft;
var initialPosScale=document.getElementById("scalezoom").offsetLeft+38;
var initialPosJock=document.getElementById("jockey").offsetLeft;
var initialPosWire=document.getElementById("wireJockeyImg2").offsetLeft;
var initialPosExtraWire=document.getElementById("wire").offsetLeft;
var test_class,selected_index=0;

var testSamples=['10','12'];
/// canvas for back ground image
var canvas = document.getElementById('mainCanvas');
canvas.width = 570;
canvas.height = 440;
var ctx = canvas.getContext('2d');

var img = new Image;

img.onload = function(){ ctx.drawImage(img,0,0);
 };
img.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/MeterBridge.png';

// function called on loading..
window.onload = function init(){

	variableArray=[('Arrangement of Resistors:'),('Jockey Position(cm):'),('Please select Resistance')];
	$(document).ready(function() {
	$('#reset').attr('value',('Reset'));
	$('#exp').attr('value',('Start Experiment'));
	$('#key').attr('value',('Insert Key'));
	connectionOptions=[("Single"),("Parallel")];
	document.getElementById('resistorLbl').innerHTML=(variableArray[0]);
	jockeySliderLbl=(variableArray[1]);
	document.getElementById('ResBoxLbl').innerHTML=("Select Resistance");
	document.getElementById('small').innerHTML=("Small");
	document.getElementById("tooltip1").innerHTML=(variableArray[2]);
    $("#input1").html(jockeySliderLbl+" "+1);
	});
	for(var x=0;x<testSamples.length;x++){
		test_class=testSamples.indexOf(testType);
	}
	if(test_class == "1"){		
		document.getElementById("expName").innerHTML=("MetreBridge-Law of Combination of resistors");	
	}else{
		document.getElementById("expName").innerHTML=("Equivalent Resistance of Resistors (Parallel)");	
	
	}
	addintoDropDown( $('#resistors'),connectionOptions); /** Adding material names to drop down list */
	
}
  /** Adding items to the drop down list */
  function addintoDropDown(getId,valueSet){	
	var _selected = getId;
	$.each(valueSet, function(val, text){
	  _selected.append(
	    $('<option></option>').val(val).html(text)
	  );
    });
  }	

/// canvas for meter bridge
var mBridgeCanvas = document.getElementById('mBridge');
mBridgeCanvas.width = 928;
mBridgeCanvas.height = 98;
var mBridgeCtx = mBridgeCanvas.getContext('2d');
var mBridgeImg = new Image;
mBridgeImg.onload = function(){ mBridgeCtx.drawImage(mBridgeImg,0,0);
 };
mBridgeImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/MeterBridgeImg.png';


/// canvas for galvanometer
var galvanometerCanvas = document.getElementById('galvanometer');
galvanometerCanvas.width = 81;
galvanometerCanvas.height = 71;
var galvanometerCtx = galvanometerCanvas.getContext('2d');
var galvanometerImg = new Image;
galvanometerImg.onload = function(){ galvanometerCtx.drawImage(galvanometerImg,0,0);
 };
galvanometerImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/Gal.png';

/// canvas for zoomed galvanometer
var galvanometerZoomCanvas = document.getElementById('zoomgalvanometer');
galvanometerZoomCanvas.width = 250;
galvanometerZoomCanvas.height = 160;
var galvanometerZoomCtx = galvanometerZoomCanvas.getContext('2d');
var galvanometerZoomImg = new Image;
galvanometerZoomImg.onload = function(){ galvanometerZoomCtx.drawImage(galvanometerZoomImg,0,0);
 };
galvanometerZoomImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/Zoomgalvano.png';

//canvas for zoomed galvanometer BG
var zoomBGCanvas = document.getElementById('zoomBG');
zoomBGCanvas.width = 271;
zoomBGCanvas.height = 180;
var zoomBGCtx = zoomBGCanvas.getContext('2d');
var zoomBGImg = new Image;
zoomBGImg.onload = function(){zoomBGCtx.drawImage(zoomBGImg,0,0);
 };
zoomBGImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/galBackground.png';


/// canvas for scale
var scaleCanvas = document.getElementById('scale');
scaleCanvas.width = 1300;
scaleCanvas.height = 250;
var scaleCtx = scaleCanvas.getContext('2d');
var scaleImg = new Image;
scaleImg.onload = function(){ scaleCtx.drawImage(scaleImg,0,0);
 };
scaleImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/scaleBG.png';


/// canvas for zoom jockey
var jockeyCanvas = document.getElementById('jockey');
jockeyCanvas.width = 25;
jockeyCanvas.height = 23;
var jockeyCtx = jockeyCanvas.getContext('2d');
var jockeyImg = new Image;
jockeyImg.onload = function(){ jockeyCtx.drawImage(jockeyImg,0,0);
 };
jockeyImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/jockey.png';

/// canvas for line1
var line1Canvas = document.getElementById('line1');
line1Canvas.width = 105;
line1Canvas.height = 75;
var line1Ctx = line1Canvas.getContext('2d');
var line1Img = new Image;
line1Img.onload = function(){ line1Ctx.drawImage(line1Img,0,0);
 };
line1Img.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/line1.png';

/// canvas for line2
var line2Canvas = document.getElementById('line2');
line2Canvas.width = 105;
line2Canvas.height = 105;
var line2Ctx = line2Canvas.getContext('2d');
var line2Img = new Image;
line2Img.onload = function(){ line2Ctx.drawImage(line2Img,0,0);
 };
line2Img.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/line2.png';

/// canvas for wire conneted with galvanometer
var wGalCanvas = document.getElementById('wireGal');
wGalCanvas.width = 195;
wGalCanvas.height = 87;
var wGalCtx = wGalCanvas.getContext('2d');
var wGalImg = new Image;
wGalImg.onload = function(){ wGalCtx.drawImage(wGalImg,0,0);
 };
wGalImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/wGal.png';

/// canvas for wire conneted with jockey
var wJockCanvas = document.getElementById('wireJockey');
wJockCanvas.width = 362;
wJockCanvas.height =121;
var wJockCtx = wJockCanvas.getContext('2d');
var wJockImg = new Image;
wJockImg.onload = function(){ wJockCtx.drawImage(wJockImg,0,0);
 };
wJockImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/wJockey.png';

/// canvas for wire battery
var batteryCanvas = document.getElementById('battery');
batteryCanvas.width = 116;
batteryCanvas.height =96;
var batteryCtx = batteryCanvas.getContext('2d');
var batteryImg = new Image;
batteryImg.onload = function(){ batteryCtx.drawImage(batteryImg,0,0);
 };
batteryImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/battery.png';
batteryCanvas.onmouseover=function(){
	changeCursor();
}
batteryCanvas.onmouseout=function(){
	defaultCursor();
}

/// canvas for wire resistor
var resistorCanvas = document.getElementById('resistor');
resistorCanvas.width = 76;
resistorCanvas.height =82;
var resistorCtx = resistorCanvas.getContext('2d');
var resistorImg = new Image;
resistorImg.onload = function(){ resistorCtx.drawImage(resistorImg,0,0);
 };
resistorImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/resistor.png';
resistorCanvas.onmouseover=function(){
	changeCursor();
}
resistorCanvas.onmouseout=function(){
	defaultCursor();
}

var resCnst=0;
//checking checkbox...
function checkTotal() {
	var known_R = 0;
	for (i=0;i<document.listForm.choice.length;i++) {
	  if (!document.listForm.choice[i].checked) {
		 known_R = known_R + parseInt(document.listForm.choice[i].value);
		}
	}
	document.getElementById("tooltip2").innerHTML=resCnst+"ohm";
	resCnst=known_R;
	
}


/// canvas for shadow resistor

var rshadowCanvas = document.getElementById('rshadow');
rshadowCanvas.width = 59;
rshadowCanvas.height =33;
var rshadowCtx = rshadowCanvas.getContext('2d');
var rshadowImg = new Image;
rshadowImg.onload = function(){ rshadowCtx.drawImage(rshadowImg,0,0);
 };
rshadowImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/rshadow.png';

/// canvas for shadow battery

var bshadowCanvas = document.getElementById('bshadow');
bshadowCanvas.width = 83;
bshadowCanvas.height =33;
var bshadowCtx = bshadowCanvas.getContext('2d');
var bshadowImg = new Image;
bshadowImg.onload = function(){ bshadowCtx.drawImage(bshadowImg,0,0);
 };
bshadowImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/bshadow.png';

/// canvas for shadow galvanometer

var gshadowCanvas = document.getElementById('gshadow');
gshadowCanvas.width = 81;
gshadowCanvas.height =38;
var gshadowCtx = gshadowCanvas.getContext('2d');
var gshadowImg = new Image;
gshadowImg.onload = function(){ gshadowCtx.drawImage(gshadowImg,0,0);
 };
gshadowImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/gshadow.png';

/// canvas for wire connected on battery

var wBatteryCanvas = document.getElementById('wireBattery');
wBatteryCanvas.width = 372;
wBatteryCanvas.height =124;
var wBatteryCtx = wBatteryCanvas.getContext('2d');
var wBatteryImg = new Image;
wBatteryImg.onload = function(){ wBatteryCtx.drawImage(wBatteryImg,0,0);
 };
wBatteryImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/wBattery.png';

/// canvas for wire connected on Resistor

var wResCanvas = document.getElementById('wireRes');
wResCanvas.width = 47;
wResCanvas.height =63;
var wResCtx = wResCanvas.getContext('2d');
var wResImg = new Image;
wResImg.onload = function(){ wResCtx.drawImage(wResImg,0,0);
 };
wResImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/wResistor.png';


// show tooltip
document.getElementById("tooltip").innerHTML="1cm";
resCnst=document.getElementById("tooltip2").innerHTML=0;
document.getElementById("tooltip2").innerHTML=resCnst+"ohm";

// materials visible hidden
document.getElementById("wireBattery").style.visibility='hidden';
document.getElementById("battery").style.visibility='hidden';
document.getElementById("resistor").style.visibility='hidden';
document.getElementById("rshadow").style.visibility='hidden';
document.getElementById("bshadow").style.visibility='hidden';
document.getElementById("wireRes").style.visibility='hidden';
document.getElementById("connector").style.visibility='hidden';
document.getElementById("connector2").style.visibility='hidden';
document.getElementById("tooltip1").style.visibility='hidden';
document.getElementById("tooltip2").style.visibility='hidden';
document.getElementById("box").style.visibility='hidden';
document.getElementById("close").style.visibility='hidden';
document.getElementById("wireOnStage").style.visibility='hidden';
document.getElementById("resValue").style.visibility='hidden';

// starting the experiment
function startExp(){
	document.getElementById("tooltip1").style.visibility='visible';
	startFlag=true;
}
resistorCanvas.onclick=function(){
	if(startFlag==true){
		showBox();
	}else{
		hideBox();
	}
}

function showBox(){
	document.getElementById("box").style.visibility='visible';
	document.getElementById("close").style.visibility='visible';
	document.getElementById("resValue").style.visibility='visible';
	
}
function hideBox(){
	document.getElementById("box").style.visibility='hidden';
	document.getElementById("close").style.visibility='hidden';
	document.getElementById("resValue").style.visibility='hidden';
}

function jockeyPosition(newValue){
	//sliderValue=roundNumber(newValue,2);
	//document.getElementById("input1").innerHTML=sliderValue;
	var val=newValue;
	sliderValue=newValue;
	$("#input1").html(jockeySliderLbl+" "+sliderValue)
	//sliderValue=roundNumber(e.slice(0,1000),1);
	//document.getElementById("divJock").innerHTML=sliderValue;
	document.getElementById("tooltip").innerHTML=sliderValue+"cm";
	position1=initialPosOftooltip+(sliderValue*4);
	position2=initialPosScale-(sliderValue*34.8);
	position3=initialPosJock+(sliderValue*4.52);
	position4=initialPosWire+(sliderValue*3.8);
	document.getElementById("tooltip").style.left=position1+"px";
	document.getElementById("scalezoom").style.left=position2+"px";
	document.getElementById("jockey").style.left=position3+"px";
	document.getElementById("wireJockeyImg2").style.left=position4+"px";
	
	if(sliderValue>66){
		position5=initialPosExtraWire+(sliderValue*2);
		document.getElementById("wire").style.visibility='visible';
		document.getElementById("wire").style.left=position5+"px";
	}else{
		document.getElementById("wire").style.visibility='hidden';
	}
	
	if(selected_index==0){
	if(Wireclk=="parallelWire1"){
		//alert(result1)
		if(sliderValue==result1){
		document.getElementById("needleRotate1").style.visibility='hidden';
	    document.getElementById("needleRotate2").style.visibility='hidden';
	    document.getElementById("firstneedleRotate1").style.visibility='visible';
	    document.getElementById("firstneedleRotate2").style.visibility='visible';
	    document.getElementById("needleRotate3").style.visibility='hidden';
	    document.getElementById("firstneedleRotate3").style.visibility='hidden';
		}else {
			if(sliderValue>result1){ 
			document.getElementById("needleRotate3").style.visibility='visible';
			document.getElementById("needleRotate1").style.visibility='hidden';
			document.getElementById("needleRotate2").style.visibility='hidden'	
			document.getElementById("firstneedleRotate1").style.visibility='hidden';
			document.getElementById("firstneedleRotate2").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='visible';
			document.getElementById("firstneedleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("firstneedleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("firstneedleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("firstneedleRotate3").style.MozTransformOrigin='10% 100%';	
			document.getElementById("needleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("needleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("needleRotate3").style.MozTransformOrigin='10% 100%';	 
				
			}else if(sliderValue<result1){
			document.getElementById("needleRotate1").style.visibility='visible';
			document.getElementById("needleRotate3").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='hidden';
	        document.getElementById("needleRotate2").style.visibility='visible';
	        document.getElementById("firstneedleRotate1").style.visibility='hidden';
	        document.getElementById("firstneedleRotate2").style.visibility='hidden';
            document.getElementById("needleRotate1").style.MozTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.MozTransform ='rotate(' +-sliderValue*.011+'rad)';	
			document.getElementById("needleRotate1").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.webkitTransform = 'rotate(' +-sliderValue*.011 + 'rad)';
			document.getElementById("needleRotate2").style.MozTransformOrigin='10% 100%';
			document.getElementById("needleRotate2").style.MozTransform ='rotate(' +-sliderValue*.012 + 'rad)';	
			document.getElementById("needleRotate2").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate2").style.webkitTransform = 'rotate(' +-sliderValue*.012+'rad)';
			}
		}
    }if(Wireclk=="parallelWire2"){
	if(sliderValue==result2){
		//alert(result2)
	    document.getElementById("needleRotate1").style.visibility='hidden';
	    document.getElementById("needleRotate2").style.visibility='hidden';
	    document.getElementById("firstneedleRotate1").style.visibility='visible';
	    document.getElementById("firstneedleRotate2").style.visibility='visible';
	    document.getElementById("needleRotate3").style.visibility='hidden';
	    document.getElementById("firstneedleRotate3").style.visibility='hidden';
	}else{
            if(sliderValue>result2){ 
			document.getElementById("needleRotate3").style.visibility='visible';
			document.getElementById("needleRotate1").style.visibility='hidden';
			document.getElementById("needleRotate2").style.visibility='hidden'	
			document.getElementById("firstneedleRotate1").style.visibility='hidden';
			document.getElementById("firstneedleRotate2").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='visible';
			document.getElementById("firstneedleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("firstneedleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("firstneedleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("firstneedleRotate3").style.MozTransformOrigin='10% 100%';	
			document.getElementById("needleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("needleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("needleRotate3").style.MozTransformOrigin='10% 100%';	 
				
			}else if(sliderValue<result2){
			document.getElementById("needleRotate1").style.visibility='visible';
			document.getElementById("needleRotate3").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='hidden';
	        document.getElementById("needleRotate2").style.visibility='visible';
	        document.getElementById("firstneedleRotate1").style.visibility='hidden';
	        document.getElementById("firstneedleRotate2").style.visibility='hidden';
            document.getElementById("needleRotate1").style.MozTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.MozTransform ='rotate(' +-sliderValue*.011+'rad)';	
			document.getElementById("needleRotate1").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.webkitTransform = 'rotate(' +-sliderValue*.011 + 'rad)';
			document.getElementById("needleRotate2").style.MozTransformOrigin='10% 100%';
			document.getElementById("needleRotate2").style.MozTransform ='rotate(' +-sliderValue*.012 + 'rad)';	
			document.getElementById("needleRotate2").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate2").style.webkitTransform = 'rotate(' +-sliderValue*.012+'rad)';
			}
	   }
	 }
   }
	if(selected_index==1){

	if(sliderValue==L){
		document.getElementById("needleRotate1").style.visibility='hidden';
	    document.getElementById("needleRotate2").style.visibility='hidden';
	    document.getElementById("firstneedleRotate1").style.visibility='visible';
	    document.getElementById("firstneedleRotate2").style.visibility='visible';
	    document.getElementById("needleRotate3").style.visibility='hidden';
	    document.getElementById("firstneedleRotate3").style.visibility='hidden';
		}else{
			
            if(sliderValue>L){ 
			document.getElementById("needleRotate3").style.visibility='visible';
			document.getElementById("needleRotate1").style.visibility='hidden';
			document.getElementById("needleRotate2").style.visibility='hidden'	
			document.getElementById("firstneedleRotate1").style.visibility='hidden';
			document.getElementById("firstneedleRotate2").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='visible';
			document.getElementById("firstneedleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("firstneedleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("firstneedleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("firstneedleRotate3").style.MozTransformOrigin='10% 100%';	
			document.getElementById("needleRotate3").style.MozTransform ='rotate(' +-sliderValue*.003+'rad)';	
			document.getElementById("needleRotate3").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate3").style.webkitTransform = 'rotate(' +-sliderValue*.003 + 'rad)';
			document.getElementById("needleRotate3").style.MozTransformOrigin='10% 100%';	 
				
			}else if(sliderValue<L){
			document.getElementById("needleRotate1").style.visibility='visible';
			document.getElementById("needleRotate3").style.visibility='hidden';
			document.getElementById("firstneedleRotate3").style.visibility='hidden';
	        document.getElementById("needleRotate2").style.visibility='visible';
	        document.getElementById("firstneedleRotate1").style.visibility='hidden';
	        document.getElementById("firstneedleRotate2").style.visibility='hidden';
            document.getElementById("needleRotate1").style.MozTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.MozTransform ='rotate(' +-sliderValue*.011+'rad)';	
			document.getElementById("needleRotate1").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate1").style.webkitTransform = 'rotate(' +-sliderValue*.011 + 'rad)';
			document.getElementById("needleRotate2").style.MozTransformOrigin='10% 100%';
			document.getElementById("needleRotate2").style.MozTransform ='rotate(' +-sliderValue*.012 + 'rad)';	
			document.getElementById("needleRotate2").style.webkitTransformOrigin='100% 100%';
			document.getElementById("needleRotate2").style.webkitTransform = 'rotate(' +-sliderValue*.012+'rad)';
			}
		}
	  }
    }


// inserting jockey 
function insertKey(){
	document.getElementById("jock").value="1";
	/*if ( $.browser.mozilla == true){		
			$('.jock').slider({ 
				value:"1",
			 });
	}*/
	if(selected_index==0){
	if(document.getElementById("key").value==('Insert Key')){
	document.getElementById("connector").style.visibility='hidden';
	document.getElementById("connector2").style.visibility='visible';
	document.getElementById("jock").disabled=false;	
	/*if ( $.browser.mozilla == true){		
			$('.jock').slider({ 
				disabled: false,
			 });
	}*/
	document.getElementById("key").value=('Remove key');
	//document.getElementById("jock").disabled=false;	
	document.getElementById("needleRotate1").style.visibility='visible';
	document.getElementById("needleRotate2").style.visibility='visible';
	document.getElementById("firstneedleRotate1").style.visibility='hidden';
	document.getElementById("firstneedleRotate2").style.visibility='hidden';
	document.getElementById("needleRotate3").style.visibility='hidden';
	document.getElementById("firstneedleRotate3").style.visibility='hidden';
	}else if(document.getElementById("key").value==('Remove key')){
	document.getElementById("connector").style.visibility='visible';
	document.getElementById("connector2").style.visibility='hidden';
	document.getElementById("key").value=('Insert Key');
	sliderValue=1;
	document.getElementById("jock").disabled=true;
	/*if ( $.browser.mozilla == true){		
		$('.jock').slider({ 
			disabled: true,
		});
	}*/
	//document.getElementById("divJock").innerHTML=sliderValue;
	document.getElementById("tooltip").innerHTML=sliderValue+"cm";
	position1=initialPosOftooltip+(sliderValue*4);
	position2=initialPosScale-(sliderValue*33);
	position3=initialPosJock+(sliderValue*5);
	position4=initialPosWire+(sliderValue*3.8);
	document.getElementById("tooltip").style.left=position1+"px";
	document.getElementById("scalezoom").style.left=position2+"px";
	document.getElementById("jockey").style.left=position3+"px";
	document.getElementById("wireJockeyImg2").style.left=position4+"px";
	document.getElementById("needleRotate1").style.visibility='hidden';
	document.getElementById("needleRotate2").style.visibility='hidden';
	document.getElementById("needleRotate3").style.visibility='hidden';
	document.getElementById("firstneedleRotate3").style.visibility='hidden';
	document.getElementById("firstneedleRotate1").style.visibility='visible';
	document.getElementById("firstneedleRotate2").style.visibility='hidden';
	//document.getElementById("jock").disabled=true;
	//document.getElementById("jock").value=1;
	}
  }else if(selected_index==1){
	if(document.getElementById("key").value==('Insert Key')){
	document.getElementById("connector").style.visibility='hidden';
	document.getElementById("connector2").style.visibility='visible';
	document.getElementById("jock").disabled=false;
	/*if ( $.browser.mozilla == true){		
			$('.jock').slider({ 
				disabled: false,
			 });
	}*/
	document.getElementById("key").value=('Remove key');
	//document.getElementById("jock").disabled=false;	
	document.getElementById("needleRotate1").style.visibility='visible';
	document.getElementById("needleRotate2").style.visibility='visible';
	document.getElementById("firstneedleRotate1").style.visibility='hidden';
	document.getElementById("firstneedleRotate2").style.visibility='hidden';
	document.getElementById("needleRotate3").style.visibility='hidden';
	document.getElementById("firstneedleRotate3").style.visibility='hidden';
	 
	}else if(document.getElementById("key").value==('Remove key')){
	document.getElementById("connector").style.visibility='visible';
	document.getElementById("connector2").style.visibility='hidden';	
	document.getElementById("key").value=('Insert Key');
	sliderValue=1;
	document.getElementById("jock").disabled=true;
	/*if ( $.browser.mozilla == true){		
			$('.jock').slider({ 
				disabled: true,
			 });
	}
*/	//document.getElementById("divJock").innerHTML=sliderValue;
	document.getElementById("tooltip").innerHTML=sliderValue+"cm";
	position1=initialPosOftooltip+(sliderValue*4);
	position2=initialPosScale-(sliderValue*33);
	position3=initialPosJock+(sliderValue*5);
	position4=initialPosWire+(sliderValue*3.8);
	document.getElementById("tooltip").style.left=position1+"px";
	document.getElementById("scalezoom").style.left=position2+"px";
	document.getElementById("jockey").style.left=position3+"px";
	document.getElementById("wireJockeyImg2").style.left=position4+"px";
	document.getElementById("needleRotate1").style.visibility='hidden';
	document.getElementById("needleRotate2").style.visibility='hidden';
	document.getElementById("firstneedleRotate1").style.visibility='visible';
	document.getElementById("firstneedleRotate2").style.visibility='visible';
	document.getElementById("needleRotate3").style.visibility='hidden';
	document.getElementById("firstneedleRotate3").style.visibility='hidden';
	//document.getElementById("jock").disabled=true;
	//document.getElementById("jock").value=1;
	}  
  }
	  
 
}

// show wire
function showParellelWire(e){
	
	document.getElementById("resistors").disabled=true;
	Wireclk=e.id;
	document.getElementById("reset").disabled=false;
	//if(Res=="Single"){
	if(selected_index==0){
	wireflag=1;
	if((Wireclk=="parallelWire1")){
		    document.getElementById("parallelWire1").style.visibility='hidden'; 
		    document.getElementById("Pwire").style.visibility='visible'; 
		    document.getElementById("parallelWire2").style.visibility='hidden'; 
		    document.getElementById("Pwire2").style.visibility='visible';  
			 if((document.getElementById("wireOnStage").style.visibility=='visible')){	
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden';
			 }else if((document.getElementById("wireOnStage").style.visibility=='hidden')){
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden'; 
			 }
          }
		  if((Wireclk=="parallelWire2")){
		    document.getElementById("parallelWire2").style.visibility='hidden'; 
		    document.getElementById("Pwire2").style.visibility='visible';
			document.getElementById("parallelWire1").style.visibility='hidden'; 
		    document.getElementById("Pwire").style.visibility='visible';  
			 if((document.getElementById("wireOnStage").style.visibility=='visible')){	
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden';
			 }else if((document.getElementById("wireOnStage").style.visibility=='hidden')){
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden'; 
			 }
          }	
		}else if(selected_index==1){
			if((Wireclk=="parallelWire2")){
		    document.getElementById("parallelWire2").style.visibility='hidden'; 
		    document.getElementById("Pwire2").style.visibility='visible';  
			 if((document.getElementById("wireOnStage").style.visibility=='visible')){	
			 document.getElementById("wireOnStage").style.visibility='hidden';
			 document.getElementById("wireTotalImg").style.visibility='visible';
		//	 document.getElementById("exp").disabled=false;	
			 }else if((document.getElementById("wireOnStage").style.visibility=='hidden')){
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden';
			 // document.getElementById("exp").disabled=true; 
			 }
			 wireflag--;console.log(wireflag);
          }
		  if((Wireclk=="parallelWire1")){
			   document.getElementById("parallelWire1").style.visibility='hidden'; 
		       document.getElementById("Pwire").style.visibility='visible'; 
			 if((document.getElementById("wireOnStage").style.visibility=='visible')){	
			 document.getElementById("wireOnStage").style.visibility='hidden';
			 document.getElementById("wireTotalImg").style.visibility='visible';
			 // document.getElementById("exp").disabled=false;
			 }else if((document.getElementById("wireOnStage").style.visibility=='hidden')){
			 document.getElementById("wireOnStage").style.visibility='visible';
			 document.getElementById("wireTotalImg").style.visibility='hidden';
			 // document.getElementById("exp").disabled=true; 
			 }
			 wireflag--;console.log(wireflag);
          }
          if(wireflag == -2){wireflag = 1;console.log("FF: "+wireflag);}
		}
	startfnenable();	
}
// for show resistor on stage 
function showResistor(e){
	
 resflag=1;
 	document.getElementById("resistors").disabled=true;
	document.getElementById("resistor_control").style.visibility='hidden';
    document.getElementById("resistorInsbleImg").style.visibility='visible';
	document.getElementById("resistor").style.visibility='visible';
	document.getElementById("wireRes").style.visibility='visible';
	document.getElementById("rshadow").style.visibility='visible';
	document.getElementById("tooltip2").style.visibility='visible';
	document.getElementById("resistor_control").disabled=true;
	document.getElementById("resistors").disabled=true;
	startfnenable();
}
function startfnenable()
{
if((batteryflag==1)&&(resflag==1)&&(wireflag==1))
{
	document.getElementById("exp").disabled=false;
}
}
// for show battery on stage
function showBattery(e){ 
batteryflag=1;
    document.getElementById("battery_control").style.visibility='hidden';
    document.getElementById("bateryInsbleImg").style.visibility='visible';
	document.getElementById("battery").style.visibility='visible';
	document.getElementById("wireBattery").style.visibility='visible';
	document.getElementById("bshadow").style.visibility='visible';
	document.getElementById("connector").style.visibility='visible';
	document.getElementById("battery_control").disabled=true;
	document.getElementById("resistors").disabled=true;
startfnenable();
}

// Close button click...
function closeBox(){
	document.getElementById("box").style.visibility='hidden';
	document.getElementById("close").style.visibility='hidden';
	document.getElementById("tooltip1").style.visibility='hidden';
	document.getElementById("resValue").style.visibility='hidden';
	document.getElementById("exp").disabled=true;
	document.getElementById("key").disabled=false;
	W1=document.getElementById("parallelWire1").value=5;
	W2=document.getElementById("parallelWire2").value=4;
	//W2=document.getElementById("parallelWire2").item(0).value=4;
	checkTotal();
	calculation();
}

//value rounded..
function roundNumber(num, dec) {
	var result = Math.round(num*Math.pow(10,dec))/Math.pow(10,dec);
	return result;
}

//To change the cursor type..
function changeCursor(){
	document.body.style.cursor='pointer';
}
function defaultCursor(){
	document.body.style.cursor='default';
}
//combo change....
function resistorsCombo(thelist2, theinput){
	selected_index=document.getElementById("resistors").selectedIndex;
    document.getElementById("key").disabled=true;
	Res=document.getElementsByName('resistors').item(0).value;
	document.getElementById("resistor_control").style.visibility='visible';
    document.getElementById("resistorInsbleImg").style.visibility='hidden';
	document.getElementById("battery_control").style.visibility='visible';
    document.getElementById("bateryInsbleImg").style.visibility='hidden';
	document.getElementById("resistor").style.visibility='hidden';
	document.getElementById("wireRes").style.visibility='hidden';
	document.getElementById("rshadow").style.visibility='hidden';
	document.getElementById("battery").style.visibility='hidden';
	document.getElementById("wireBattery").style.visibility='hidden';
	document.getElementById("bshadow").style.visibility='hidden';
	document.getElementById("connector").style.visibility='hidden';
	document.getElementById("connector2").style.visibility='hidden';
	document.getElementById("tooltip2").style.visibility='hidden';
	document.getElementById("wireOnStage").style.visibility='hidden';
	document.getElementById("wireTotalImg").style.visibility='hidden';
	document.getElementById("needleRotate1").style.visibility='hidden';
	document.getElementById("needleRotate2").style.visibility='hidden';
	document.getElementById("firstneedleRotate1").style.visibility='visible';
	document.getElementById("firstneedleRotate2").style.visibility='visible';
	//document.getElementById("jock").disabled=true;
	//document.getElementById("jock").value=1;
	//closeBox();
}

var result1;
var result2;
var result3;
var rss=0;
var L;
//calculating function.
function calculation(){
	W1=5;
	W2=4;
	if(selected_index==0){
	if(Wireclk=="parallelWire1"){
	
	result1=((100*resCnst)/(resCnst+W1));
	result1=Math.round(result1*10)/10
	//alert(result1);
	}else if(Wireclk=="parallelWire2"){
	result2=((100*resCnst)/(resCnst+W2));
	result2=Math.round(result2*10)/10
	//alert(result2);
	}
	}
	else if(selected_index==1){
	result3=((W1*W2)/(W1+W2));
	L=((100*resCnst)/(result3+resCnst));
	L=Math.round(L*10)/10
	//alert(L);
	
  }
}

//for slider input box restriction...

 function onlyNumbers(evt)
{
	
	var e = event || evt; // for trans-browser compatibility

	var charCode = e.which || e.keyCode;
	
	if(((charCode == 190) ||(charCode == 110))||(charCode >= 96 && charCode <= 105))
	{
	
		return true;
	}
	

	 if ((charCode > 31) && (charCode < 48 || charCode > 57))
	{
		
		return false;
	
	}

	
	
}

