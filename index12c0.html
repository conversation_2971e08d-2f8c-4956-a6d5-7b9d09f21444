<title>index</title>
<script src="../../../../Scripts/swfobject_modified.js" type="text/javascript"></script>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
}
-->
</style>
 

<!-- Mirrored from amrita.olabs.edu.in/olab/PHY/EMM/MeterBridge-Single-And-Parallel/index.html?elink_videoID=s0Pk34_yN-Y&linktoken=56e5ccd284aec70eba255126df4b680c&elink_lan=ml-IN&elink_title=(%e0%b4%b8%e0%b4%ae%e0%b4%be%e0%b4%a8%e0%b5%8d%e0%b4%a4%e0%b4%b0%e0%b4%ae%e0%b4%be%e0%b4%af)%20%e0%b4%aa%e0%b5%8d%e0%b4%b0%e0%b4%a4%e0%b4%bf%e0%b4%b0%e0%b5%8b%e0%b4%a7%e0%b4%95%e0%b4%99%e0%b5%8d%e0%b4%99%e0%b4%b3%e0%b5%81%e0%b4%9f%e0%b5%86%20%e0%b4%a4%e0%b5%81%e0%b4%b2%e0%b5%8d%e0%b4%af%e0%b4%ae%e0%b4%be%e0%b4%af%20%e0%b4%aa%e0%b5%8d%e0%b4%b0%e0%b4%a4%e0%b4%bf%e0%b4%b0%e0%b5%8b%e0%b4%a7%e0%b4%82 by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:49:03 GMT -->
<!-- AmritaCREATE 2023 --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by AmritaCREATE 2023 -->
<body>
<div align="center">
<script type="text/javascript">
//get lan variable from the url.
function getValue( name ) {
   
  name = name.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");
  var regexS = "[\\?&]"+name+"=([^&#]*)";
  var regex = new RegExp( regexS );
  var results = regex.exec( window.location.href );
  if( results != null )
    return results[1];
  else
    return false;
  }
  
var lan=getValue("elink_lan");
var videoID=getValue("elink_videoID");
var title=getValue("elink_title");

if(!lan){
	lan="en-US"; //default language
}

document.write(
  '<object id="FlashID" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="800" height="600">\n'+
  '<param name="movie" value="index.swf" />\n'+
  '<param name="FlashVars" value=\"elink_lan='+lan+'"/> \n'+ 
  '<param name="quality" value="high" />\n'+
  '<param name="wmode" value="opaque" />\n'+
  '<param name="swfversion" value="********" />\n'+
 // <!-- This param tag prompts users with Flash Player 6.0 r65 and higher to download the latest version of Flash Player. Delete it if you don�t want users to see the prompt. -->
  '<param name="expressinstall" value="../../../../Scripts/expressInstall.swf" />\n'+
  '<param name="allowfullscreen" value="true" />\n'+
 // <!-- Next object tag is for non-IE browsers. So hide it from IE using IECC. -->
 // <!--[if !IE]>-->
  '<object type="application/x-shockwave-flash" data=\"index.swf?elink_lan='+lan+'" width="800" height="600">\n'+
    <!--<![endif]-->
    '<param name="quality" value="high" />\n'+
    '<param name="wmode" value="opaque" />\n'+
    '<param name="swfversion" value="********" />\n'+
	
	'<param name="FlashVars" value=\"elink_lan='+lan+'"/>\n'+ 
	'<param name="movie" value="index.swf" />\n'+
    '<param name="expressinstall" value="../../../../Scripts/expressInstall.swf" />\n'+
    '<param name="allowfullscreen" value="true" />\n'+
   // <!-- The browser displays the following alternative content for users with Flash Player 6.0 and older. -->
     '<div align="center"><iframe align="middle" width="100%" height="100%" src="../../../Common/Templates/html5/olab_new/index.html?videoID='+videoID+'&rel=0&lan='+lan+'&title='+title+'" frameborder="0" allowfullscreen></iframe>\n'+
    '</div>\n'+
    <!--[if !IE]>-->
  '</object>\n'+
  <!--<![endif]-->
'</object>\n')

</script>
</div>
</body>

<!-- Mirrored from amrita.olabs.edu.in/olab/PHY/EMM/MeterBridge-Single-And-Parallel/index.html?elink_videoID=s0Pk34_yN-Y&linktoken=56e5ccd284aec70eba255126df4b680c&elink_lan=ml-IN&elink_title=(%e0%b4%b8%e0%b4%ae%e0%b4%be%e0%b4%a8%e0%b5%8d%e0%b4%a4%e0%b4%b0%e0%b4%ae%e0%b4%be%e0%b4%af)%20%e0%b4%aa%e0%b5%8d%e0%b4%b0%e0%b4%a4%e0%b4%bf%e0%b4%b0%e0%b5%8b%e0%b4%a7%e0%b4%95%e0%b4%99%e0%b5%8d%e0%b4%99%e0%b4%b3%e0%b5%81%e0%b4%9f%e0%b5%86%20%e0%b4%a4%e0%b5%81%e0%b4%b2%e0%b5%8d%e0%b4%af%e0%b4%ae%e0%b4%be%e0%b4%af%20%e0%b4%aa%e0%b5%8d%e0%b4%b0%e0%b4%a4%e0%b4%bf%e0%b4%b0%e0%b5%8b%e0%b4%a7%e0%b4%82 by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:49:03 GMT -->
</html>
