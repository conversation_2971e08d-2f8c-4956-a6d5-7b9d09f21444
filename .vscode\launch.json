{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "program": "${workspaceFolder}/src/index.js",
            "outFiles": ["${workspaceFolder}/dist/**/*.js"],
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/ts-node"
        },
        {
            "type": "node",
            "request": "attach",
            "name": "Attach to Process",
            "port": 9229,
            "restart": true,
            "skipFiles": ["<node_internals>/**"]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Test Suite",
            "program": "${workspaceFolder}/node_modules/.bin/mocha",
            "args": ["--require", "ts-node/register", "${workspaceFolder}/test/**/*.spec.ts"],
            "outFiles": ["${workspaceFolder}/dist/**/*.js"],
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/ts-node"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with Nodemon",
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nodemon",
            "runtimeArgs": ["--inspect=9229", "${workspaceFolder}/src/index.js"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with PM2",
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/pm2",
            "runtimeArgs": ["start", "${workspaceFolder}/src/index.js", "--node-args=--inspect=9229"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with Docker",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/docker-compose",
            "runtimeArgs": ["up", "--build"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with PM2 in Docker",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/pm2-docker",
            "runtimeArgs": ["start", "${workspaceFolder}/src/index.js", "--node-args=--inspect=9229"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with Nodemon in Docker",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nodemon-docker",
            "runtimeArgs": ["--inspect=9229", "${workspaceFolder}/src/index.js"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with PM2 in Docker Compose",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/pm2-docker-compose",
            "runtimeArgs": ["start", "${workspaceFolder}/src/index.js", "--node-args=--inspect=9229"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with Nodemon in Docker Compose",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nodemon-docker-compose",
            "runtimeArgs": ["--inspect=9229", "${workspaceFolder}/src/index.js"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with PM2 in Kubernetes",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/pm2-kubernetes",
            "runtimeArgs": ["start", "${workspaceFolder}/src/index.js", "--node-args=--inspect=9229"],
            "restart": true,
            "console": "integratedTerminal"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Debugger with Nodemon in Kubernetes",
            "dockerOptions": {
                "containerName": "my-node-app",
                "portMappings": [
                    {
                        "containerPort": 9229,
                        "hostPort": 9229
                    }
                ]
            },
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nodemon-kubernetes",
            "runtimeArgs": ["--inspect=9229", "${workspaceFolder}/src/index.js"],
            "restart": true,
            "console": "integratedTerminal"
        },


        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Chrome against localhost",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        }
    ]
}